[{"/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx": "1", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx": "2", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx": "3", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx": "4", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx": "5", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx": "6", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx": "7", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx": "8", "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts": "9"}, {"size": 272, "mtime": *************, "results": "10", "hashOfConfig": "11"}, {"size": 1035, "mtime": *************, "results": "12", "hashOfConfig": "11"}, {"size": 2045, "mtime": *************, "results": "13", "hashOfConfig": "11"}, {"size": 646, "mtime": *************, "results": "14", "hashOfConfig": "11"}, {"size": 1976, "mtime": *************, "results": "15", "hashOfConfig": "11"}, {"size": 5067, "mtime": *************, "results": "16", "hashOfConfig": "11"}, {"size": 28555, "mtime": *************, "results": "17", "hashOfConfig": "18"}, {"size": 2050, "mtime": *************, "results": "19", "hashOfConfig": "11"}, {"size": 721, "mtime": *************, "results": "20", "hashOfConfig": "11"}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sxxfvo", {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ix7n9v", {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Codes/py/social-media-manager/frontend/src/index.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/App.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/Layout.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/AccountManage.tsx", ["48", "49", "50", "51"], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Codes/py/social-media-manager/frontend/src/services/api.ts", ["52"], [], {"ruleId": "53", "severity": 1, "message": "54", "line": 60, "column": 10, "nodeType": "55", "messageId": "56", "endLine": 60, "endColumn": 28}, {"ruleId": "53", "severity": 1, "message": "57", "line": 60, "column": 30, "nodeType": "55", "messageId": "56", "endLine": 60, "endColumn": 51}, {"ruleId": "53", "severity": 1, "message": "58", "line": 357, "column": 9, "nodeType": "55", "messageId": "56", "endLine": 357, "endColumn": 26}, {"ruleId": "53", "severity": 1, "message": "59", "line": 479, "column": 31, "nodeType": "55", "messageId": "56", "endLine": 479, "endColumn": 43}, {"ruleId": "53", "severity": 1, "message": "60", "line": 2, "column": 10, "nodeType": "55", "messageId": "56", "endLine": 2, "endColumn": 17}, "@typescript-eslint/no-unused-vars", "'feishuModalVisible' is assigned a value but never used.", "Identifier", "unusedVar", "'setFeishuModalVisible' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'data_summary' is assigned a value but never used.", "'message' is defined but never used."]