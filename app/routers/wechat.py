from fastapi import APIRouter, Depends, HTTPException, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from pydantic import BaseModel
from typing import Optional
import io
from app.database import get_db
from app.services.wechat_service import WeChatMPService
from app.models import PlatformAccount, DataRecord
from app.routers.auth import get_current_user
import asyncio
import json

router = APIRouter()

# 存储活跃的登录会话
login_sessions = {}

# Pydantic模型
class DataCollectionRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    data_type: str   # user_summary, article_summary

class DataDownloadRequest(BaseModel):
    start_date: str  # YYYY-MM-DD格式
    end_date: str    # YYYY-MM-DD格式
    busi: Optional[int] = 3  # 业务类型，默认3（向后兼容）
    tmpl: Optional[int] = 19  # 模板类型，默认19（向后兼容）
    data_type: Optional[str] = None  # 数据类型：content_trend, content_source, content_detail, user_channel

@router.post("/login/qrcode/{account_id}")
async def get_login_qrcode(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取微信公众号登录二维码"""
    
    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    # 创建微信服务实例（传入账号ID以支持持久化）
    wechat_service = WeChatMPService(account_id=account_id)
    qr_code = await wechat_service.get_login_qrcode()
    
    if not qr_code:
        raise HTTPException(status_code=500, detail="获取二维码失败")
    
    # 保存会话
    login_sessions[account_id] = wechat_service
    
    return {"qrcode": qr_code, "account_id": account_id}

@router.get("/login/status/{account_id}")
async def check_login_status(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查登录状态"""
    
    if account_id not in login_sessions:
        raise HTTPException(status_code=404, detail="登录会话不存在")
    
    wechat_service = login_sessions[account_id]
    is_logged_in = await wechat_service.check_login_status()
    
    if is_logged_in:
        # 保存登录状态到文件（重要！）
        await wechat_service.save_login_state()
        
        # 获取cookies并保存
        cookies = await wechat_service.get_cookies()
        
        # 更新数据库
        account = db.query(PlatformAccount).filter(PlatformAccount.id == account_id).first()
        account.login_status = True
        account.cookies = cookies
        account.last_login_time = datetime.utcnow()
        db.commit()
        
        # 清理会话
        await wechat_service.close()
        del login_sessions[account_id]
    
    return {"logged_in": is_logged_in}

@router.post("/collect-data/{account_id}")
async def collect_data(
    account_id: int,
    request: DataCollectionRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """采集微信公众号数据"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 检查账号是否已登录
    if not account.login_status:
        raise HTTPException(status_code=400, detail="账号未登录，请先登录")

    try:
        # 创建微信服务实例并恢复登录状态（传入账号ID）
        wechat_service = WeChatMPService(account_id=account_id)
        
        # 尝试加载已保存的登录状态
        if not await wechat_service.load_login_state():
            # 如果加载状态失败，说明登录状态文件丢失或过期
            # 更新数据库状态为未登录
            account.login_status = False
            account.cookies = None
            account.last_login_time = None
            db.commit()
            print(f"登录状态文件不存在或已过期，已重置账号 {account_id} 的登录状态")
            raise HTTPException(
                status_code=400, 
                detail="登录会话已过期，请重新获取二维码登录"
            )

        collected_data = None

        if request.data_type == "user_summary":
            collected_data = await wechat_service.get_user_summary_data(
                request.start_date, request.end_date
            )
        elif request.data_type == "article_summary":
            collected_data = await wechat_service.get_article_summary_data(
                request.start_date, request.end_date
            )
        else:
            raise HTTPException(status_code=400, detail="不支持的数据类型")

        if collected_data is None:
            raise HTTPException(status_code=500, detail="数据采集失败")

        # 保存数据到数据库
        data_record = DataRecord(
            account_id=account_id,
            data_type=request.data_type,
            date=datetime.strptime(request.start_date, "%Y-%m-%d"),
            data=collected_data
        )
        db.add(data_record)
        db.commit()
        db.refresh(data_record)

        await wechat_service.close()

        return {
            "message": "数据采集成功",
            "data_record_id": data_record.id,
            "data": collected_data
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"数据采集失败: {str(e)}")

@router.get("/data/{account_id}")
async def get_collected_data(
    account_id: int,
    data_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取已采集的数据"""

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")

    # 构建查询条件
    query = db.query(DataRecord).filter(DataRecord.account_id == account_id)

    if data_type:
        query = query.filter(DataRecord.data_type == data_type)

    if start_date:
        query = query.filter(DataRecord.date >= datetime.strptime(start_date, "%Y-%m-%d"))

    if end_date:
        query = query.filter(DataRecord.date <= datetime.strptime(end_date, "%Y-%m-%d"))

    # 按日期降序排列
    data_records = query.order_by(DataRecord.date.desc()).all()

    return {
        "account_id": account_id,
        "account_name": account.name,
        "data_records": [
            {
                "id": record.id,
                "data_type": record.data_type,
                "date": record.date.strftime("%Y-%m-%d"),
                "data": record.data,
                "created_at": record.created_at
            }
            for record in data_records
        ]
    }

@router.post("/download-data/{account_id}")
async def download_data_excel(
    account_id: int,
    request: DataDownloadRequest,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """下载微信公众号数据Excel文件"""
    print(f"下载请求详情: account_id={account_id}, request={request}")

    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        print(f"账号不存在: account_id={account_id}, user_id={current_user.id}")
        raise HTTPException(status_code=404, detail="账号不存在")
    
    print(f"找到账号: {account.name}, 登录状态: {account.login_status}")
    
    # 检查账号是否已登录
    if not account.login_status:
        print("账号未登录")
        raise HTTPException(status_code=400, detail="账号未登录，请先登录")
    
    try:
        # 如果有活跃的登录会话，使用它
        if account_id in login_sessions:
            wechat_service = login_sessions[account_id]
        else:
            # 创建新的微信服务实例（传入账号ID）
            wechat_service = WeChatMPService(account_id=account_id)
            # 尝试加载已保存的登录状态
            if not await wechat_service.load_login_state():
                # 如果加载状态失败，说明登录状态文件丢失或过期
                # 更新数据库状态为未登录
                account.login_status = False
                account.cookies = None
                account.last_login_time = None
                db.commit()
                print(f"登录状态文件不存在或已过期，已重置账号 {account_id} 的登录状态")
                raise HTTPException(
                    status_code=400, 
                    detail="登录会话已过期，请重新获取二维码登录"
                )
        
        print("开始下载数据")
        # 下载Excel数据
        excel_data = await wechat_service.download_data_excel(
            begin_date=request.start_date,
            end_date=request.end_date,
            busi=request.busi,
            tmpl=request.tmpl,
            data_type=request.data_type
        )
        
        if not excel_data:
            raise HTTPException(status_code=500, detail="下载数据失败")

        # 生成文件名，包含账号ID和数据类型
        data_type_suffix = f"_{request.data_type}" if request.data_type else ""
        filename = f"wechat_data_account_{account_id}{data_type_suffix}_{request.start_date}_to_{request.end_date}.xlsx"
        
        # 返回文件流响应
        return StreamingResponse(
            io.BytesIO(excel_data),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
                "Content-Length": str(len(excel_data))
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载失败22: {str(e)}")

@router.get("/download-data/{account_id}")
async def download_data_excel_get(
    account_id: int,
    start_date: str,
    end_date: str,
    busi: int = 3,
    tmpl: int = 19,
    data_type: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """GET方式下载微信公众号数据Excel文件（用于前端直接链接）"""
    print(f"收到下载请求: account_id={account_id}, start_date={start_date}, end_date={end_date}, data_type={data_type}")

    # 调用POST版本的下载方法
    request = DataDownloadRequest(
        start_date=start_date,
        end_date=end_date,
        busi=busi,
        tmpl=tmpl,
        data_type=data_type
    )
    
    return await download_data_excel(account_id, request, current_user, db)

@router.post("/logout/{account_id}")
async def logout_account(
    account_id: int,
    clear_saved_state: bool = True,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """注销指定微信公众号账号的登录状态"""
    print(f"收到注销请求: account_id={account_id}, clear_saved_state={clear_saved_state}")
    
    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    try:
        logout_success = False
        
        # 如果有活跃的登录会话，使用它进行注销
        if account_id in login_sessions:
            print(f"找到活跃的登录会话，开始注销...")
            wechat_service = login_sessions[account_id]
            logout_success = await wechat_service.logout(clear_saved_state=clear_saved_state)
            
            # 从活跃会话中移除
            del login_sessions[account_id]
            print(f"已从活跃会话中移除账号 {account_id}")
        else:
            # 没有活跃会话，但可能有保存的登录状态
            print(f"没有活跃会话，尝试清理保存的登录状态...")
            wechat_service = WeChatMPService(account_id=account_id)
            
            # 只清理保存的状态文件
            if clear_saved_state:
                wechat_service._clear_saved_login_state()
                logout_success = True
            else:
                logout_success = True
        
        # 更新数据库中的账号状态
        account.login_status = False  # 重要：更新登录状态
        account.cookies = None  # 清除保存的cookies
        account.last_login_time = None  # 清除最后登录时间
        db.commit()
        
        return {
            "success": logout_success,
            "message": "注销成功" if logout_success else "注销完成，但可能存在部分问题",
            "account_id": account_id,
            "cleared_saved_state": clear_saved_state
        }
        
    except Exception as e:
        print(f"注销过程中发生错误: {e}")
        
        # 即使出错也要尝试清理会话
        try:
            if account_id in login_sessions:
                del login_sessions[account_id]
        except:
            pass
        
        # 尝试强制清理
        try:
            wechat_service = WeChatMPService(account_id=account_id)
            await wechat_service.force_logout()
        except:
            pass
        
        raise HTTPException(status_code=500, detail=f"注销失败: {str(e)}")

@router.post("/force-logout/{account_id}")
async def force_logout_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """强制注销指定微信公众号账号（忽略所有错误）"""
    print(f"收到强制注销请求: account_id={account_id}")
    
    # 查询账号（确保属于当前用户）
    account = db.query(PlatformAccount).filter(
        PlatformAccount.id == account_id,
        PlatformAccount.user_id == current_user.id
    ).first()
    if not account:
        raise HTTPException(status_code=404, detail="账号不存在")
    
    try:
        # 清理活跃会话
        if account_id in login_sessions:
            try:
                wechat_service = login_sessions[account_id]
                await wechat_service.force_logout()
            except:
                pass
            del login_sessions[account_id]
            print(f"已强制清理活跃会话: {account_id}")
        
        # 强制清理保存的状态
        try:
            wechat_service = WeChatMPService(account_id=account_id)
            await wechat_service.force_logout()
        except:
            pass
        
        # 更新数据库状态
        account.login_status = False  # 重要：更新登录状态
        account.cookies = None  # 清除保存的cookies
        account.last_login_time = None  # 清除最后登录时间
        db.commit()
        
        return {
            "success": True,
            "message": "强制注销完成",
            "account_id": account_id
        }
        
    except Exception as e:
        print(f"强制注销过程中发生错误: {e}")
        # 强制注销不应该失败
        return {
            "success": True,
            "message": f"强制注销完成（部分清理可能失败: {str(e)}）",
            "account_id": account_id
        }

@router.get("/logout-all")
async def logout_all_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """注销当前用户的所有微信公众号账号"""
    print(f"收到注销所有账号请求: user_id={current_user.id}")
    
    # 查询用户的所有账号
    accounts = db.query(PlatformAccount).filter(
        PlatformAccount.user_id == current_user.id,
        PlatformAccount.platform == "wechat_mp"
    ).all()
    
    if not accounts:
        return {
            "success": True,
            "message": "没有找到需要注销的账号",
            "logout_results": []
        }
    
    logout_results = []
    
    for account in accounts:
        try:
            print(f"注销账号 {account.id}...")
            
            # 清理活跃会话
            if account.id in login_sessions:
                try:
                    wechat_service = login_sessions[account.id]
                    await wechat_service.logout(clear_saved_state=True)
                except Exception as e:
                    print(f"注销账号 {account.id} 失败: {e}")
                del login_sessions[account.id]
            
            # 清理保存的状态
            try:
                wechat_service = WeChatMPService(account_id=account.id)
                wechat_service._clear_saved_login_state()
            except:
                pass
            
            # 更新数据库状态
            account.login_status = False  # 重要：更新登录状态
            account.cookies = None  # 清除保存的cookies
            account.last_login_time = None  # 清除最后登录时间
            
            logout_results.append({
                "account_id": account.id,
                "account_name": account.account_name,
                "success": True,
                "message": "注销成功"
            })
            
        except Exception as e:
            print(f"注销账号 {account.id} 过程中发生错误: {e}")
            logout_results.append({
                "account_id": account.id,
                "account_name": account.account_name,
                "success": False,
                "message": f"注销失败: {str(e)}"
            })
    
    db.commit()
    
    success_count = sum(1 for result in logout_results if result["success"])
    total_count = len(logout_results)
    
    return {
        "success": success_count == total_count,
        "message": f"批量注销完成: {success_count}/{total_count} 个账号注销成功",
        "logout_results": logout_results
    }