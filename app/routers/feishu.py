from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import logging
from typing import Optional

from app.database import get_db
from app.models import PlatformAccount, User, FeishuTable
from app.services.feishu_service import FeishuService
from app.services.data_converter import WeChatDataConverter
from app.services.wechat_service import WeChatMPService
from app.routers.auth import get_current_user

router = APIRouter(prefix="/api/feishu", tags=["feishu"])
logger = logging.getLogger(__name__)

@router.post("/create-bitable/{account_id}")
async def create_bitable_for_account(
    account_id: int,
    bitable_name: Optional[str] = None,
    folder_token: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """为指定的微信公众号账号创建飞书多维表格"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        # 检查是否已经创建过飞书表格
        if account.feishu_app_token:
            # 获取已创建的表格信息
            existing_tables = db.query(FeishuTable).filter(
                FeishuTable.account_id == account_id
            ).all()

            table_info = {}
            for table in existing_tables:
                table_info[table.data_type] = {
                    "table_id": table.feishu_table_id,
                    "table_name": table.feishu_table_name
                }

            return {
                "success": True,
                "message": "该账号已有飞书多维表格",
                "app_token": account.feishu_app_token,
                "folder_token": account.feishu_folder_token,
                "url": account.feishu_url,
                "tables": table_info
            }
        
        # 初始化飞书服务
        feishu_service = FeishuService()
        
        # 创建多维表格
        if not bitable_name:
            bitable_name = "社媒数据表"
        
        app_token = feishu_service.create_bitable(bitable_name, folder_token)
        if not app_token:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建多维表格失败"
            )
        
        # 获取微信数据类型配置
        from app.services.wechat_service import WeChatMPService
        data_types = WeChatMPService.DOWNLOAD_TEMPLATES

        created_tables = {}

        # 为每种数据类型创建表格
        for data_type, config in data_types.items():
            table_name = config.get('name', data_type)

            # 创建数据表（包含字段）
            table_id = feishu_service.create_table_with_fields(app_token, table_name, data_type)
            if not table_id:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"创建{table_name}失败"
                )

            created_tables[data_type] = {
                "table_id": table_id,
                "table_name": table_name
            }

            # 在数据库中记录表格信息
            feishu_table = FeishuTable(
                account_id=account_id,
                feishu_app_token=app_token,
                feishu_table_id=table_id,
                feishu_table_name=table_name,
                data_type=data_type,
                feishu_record_ids=[]
            )
            db.add(feishu_table)

        # 提交表格记录
        db.commit()
        
        # 保存飞书信息到数据库
        account.feishu_app_token = app_token
        # TODO: 从飞书API获取实际的folder_token和url
        account.feishu_folder_token = None  # 暂时设为None，后续可从飞书API获取
        account.feishu_url = None  # 暂时设为None，后续可从飞书API获取
        account.feishu_created_at = datetime.now(timezone.utc)

        db.commit()

        logger.info(f"成功为账号{account_id}创建飞书多维表格: {app_token}")

        return {
            "success": True,
            "message": "飞书多维表格创建成功",
            "app_token": app_token,
            "folder_token": account.feishu_folder_token,
            "url": account.feishu_url,
            "tables": created_tables,
            "bitable_name": bitable_name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建飞书多维表格失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建飞书多维表格失败: {str(e)}"
        )

@router.post("/sync-data/{account_id}")
async def sync_wechat_data_to_feishu(
    account_id: int,
    begin_date: str,
    end_date: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """同步微信公众号数据到飞书多维表格"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        # 检查是否已创建飞书表格
        if not account.feishu_app_token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请先创建飞书多维表格"
            )
        
        # 下载微信数据
        wechat_service = WeChatMPService(account_id)
        excel_data = await wechat_service.download_data_excel(begin_date, end_date)
        
        if not excel_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="下载微信数据失败，请检查登录状态"
            )
        
        # 解析Excel数据
        parsed_data = WeChatDataConverter.parse_excel_data(excel_data)
        if not parsed_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="解析微信数据失败"
            )
        
        # 初始化飞书服务
        feishu_service = FeishuService()
        
        # 获取账号的飞书表格信息
        feishu_tables = db.query(FeishuTable).filter(
            FeishuTable.account_id == account_id
        ).all()

        if not feishu_tables:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未找到飞书表格配置，请先创建飞书多维表格"
            )

        sync_results = {
            "synced_tables": {},
            "errors": []
        }
        
        # TODO: 重新实现数据同步逻辑
        # 这里需要根据新的数据结构重新实现同步逻辑
        # 暂时返回成功状态，避免破坏现有功能
        sync_results["synced_tables"]["placeholder"] = {
            "table_name": "数据同步功能正在重构中",
            "records_synced": 0
        }
        
        # 清理微信服务资源
        await wechat_service.close()
        
        logger.info(f"账号{account_id}数据同步完成: {sync_results}")
        
        return {
            "success": True,
            "message": "数据同步功能正在重构中",
            "sync_results": sync_results,
            "data_summary": {
                "note": "数据同步功能正在重构以支持新的数据类型"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步数据到飞书失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步数据失败: {str(e)}"
        )

@router.get("/bitable-info/{account_id}")
async def get_bitable_info(
    account_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取账号的飞书多维表格信息"""
    try:
        # 查找账号
        account = db.query(PlatformAccount).filter(
            PlatformAccount.id == account_id,
            PlatformAccount.user_id == current_user.id
        ).first()
        
        if not account:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="账号不存在或无权限访问"
            )
        
        if not account.feishu_app_token:
            return {
                "success": True,
                "has_bitable": False,
                "message": "尚未创建飞书多维表格"
            }
        
        # 获取飞书表格信息
        feishu_tables = db.query(FeishuTable).filter(
            FeishuTable.account_id == account_id
        ).all()

        tables_info = {}
        total_records = 0

        for table in feishu_tables:
            record_ids = table.feishu_record_ids or []
            record_count = len(record_ids) if isinstance(record_ids, list) else 0
            total_records += record_count

            tables_info[table.data_type] = {
                "table_id": table.feishu_table_id,
                "table_name": table.feishu_table_name,
                "record_count": record_count,
                "created_at": table.created_at.isoformat() if table.created_at else None,
                "updated_at": table.updated_at.isoformat() if table.updated_at else None
            }

        return {
            "success": True,
            "has_bitable": True,
            "app_token": account.feishu_app_token,
            "folder_token": account.feishu_folder_token,
            "url": account.feishu_url,
            "tables": tables_info,
            "total_record_count": total_records,
            "created_at": account.feishu_created_at.isoformat() if account.feishu_created_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取飞书表格信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取飞书表格信息失败: {str(e)}"
        )
