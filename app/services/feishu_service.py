import os
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
import lark_oapi as lark
from lark_oapi.api.bitable.v1 import *

logger = logging.getLogger(__name__)

class FeishuService:
    """飞书多维表格服务类"""
    
    def __init__(self, app_id: Optional[str] = None, app_secret: Optional[str] = None):
        """初始化飞书客户端
        
        Args:
            app_id: 飞书应用ID，如果不提供则从环境变量获取
            app_secret: 飞书应用密钥，如果不提供则从环境变量获取
        """
        self.app_id = app_id or os.getenv("FEISHU_APP_ID")
        self.app_secret = app_secret or os.getenv("FEISHU_APP_SECRET")
        
        if not self.app_id or not self.app_secret:
            raise ValueError("飞书应用ID和密钥不能为空，请检查环境变量FEISHU_APP_ID和FEISHU_APP_SECRET")
        
        # 构建客户端
        self.client = lark.Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .log_level(lark.LogLevel.INFO) \
            .build()
    
    def create_bitable(self, name: str, folder_token: Optional[str] = None) -> Optional[str]:
        """创建多维表格
        
        Args:
            name: 表格名称
            folder_token: 存放目录的token，可选
            
        Returns:
            创建成功返回app_token，失败返回None
        """
        try:
            # 构建请求
            request = CreateAppRequest.builder() \
                .request_body(ReqApp.builder()
                    .name(name)
                    .folder_token(folder_token)
                    .build()) \
                .build()
            
            # 发起请求
            response = self.client.bitable.v1.app.create(request)
            
            if not response.success():
                logger.error(f"创建多维表格失败: {response.code}, {response.msg}")
                return None
            
            app_token = response.data.app.app_token
            logger.info(f"成功创建多维表格: {name}, app_token: {app_token}")
            return app_token
            
        except Exception as e:
            logger.error(f"创建多维表格异常: {e}")
            return None
    
    def create_table_field(self, app_token: str, table_id: str, field_name: str, field_type: int = 1) -> Optional[str]:
        """在数据表中创建字段

        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            field_name: 字段名称
            field_type: 字段类型，1=多行文本，2=数字，3=单选，4=多选，5=日期，7=复选框，11=人员，13=电话号码，15=超链接，17=附件，18=单向关联，20=公式，21=双向关联，22=地理位置，23=群组，1001=创建时间，1002=最后更新时间，1003=创建人，1004=修改人，1005=自动编号

        Returns:
            创建成功返回field_id，失败返回None
        """
        try:
            # 构建请求体
            request_body = CreateAppTableFieldRequest.builder() \
                .request_body(AppTableField.builder()
                    .field_name(field_name)
                    .type(field_type)
                    .build()) \
                .app_token(app_token) \
                .table_id(table_id) \
                .build()

            # 发起请求
            response = self.client.bitable.v1.app_table_field.create(request_body)

            if not response.success():
                logger.error(f"创建字段失败: {response.code}, {response.msg}")
                return None

            field_id = response.data.field.field_id
            logger.info(f"成功创建字段: {field_name}, field_id: {field_id}")
            return field_id

        except Exception as e:
            logger.error(f"创建字段异常: {e}")
            return None

    def create_table_with_fields(self, app_token: str, table_name: str, table_type: str = "user_summary") -> Optional[str]:
        """创建数据表并添加相应字段

        Args:
            app_token: 多维表格的app_token
            table_name: 数据表名称
            table_type: 表格类型，'user_summary' 或 'article_summary'

        Returns:
            创建成功返回table_id，失败返回None
        """
        try:
            # 先创建表
            table_id = self.create_table(app_token, table_name)
            if not table_id:
                return None

            # 根据表格类型创建相应字段
            if table_type == "user_summary":
                fields_config = [
                    ("日期", 1),  # 多行文本
                    ("新增用户数", 2),  # 数字
                    ("取消关注用户数", 2),  # 数字
                    ("净增长", 2),  # 数字
                    ("累计用户数", 2)  # 数字
                ]
            elif table_type == "article_summary":
                fields_config = [
                    ("文章标题", 1),  # 多行文本
                    ("阅读数", 2),  # 数字
                    ("点赞数", 2),  # 数字
                    ("分享数", 2),  # 数字
                    ("发布时间", 1),  # 多行文本
                    ("送达人数", 2)  # 数字
                ]
            elif table_type == "content_trend":
                fields_config = [
                    ("日期", 1),  # 多行文本
                    ("阅读次数", 2),  # 数字
                    ("阅读人数", 2),  # 数字
                    ("分享次数", 2),  # 数字
                    ("分享人数", 2),  # 数字
                    ("阅读原文次数", 2),  # 数字
                    ("阅读原文人数", 2),  # 数字
                    ("收藏次数", 2),  # 数字
                    ("收藏人数", 2),  # 数字
                    ("群发篇数", 2),  # 数字
                    ("渠道", 1),  # 多行文本
                ]
            elif table_type == "content_source":
                fields_config = [
                    ("传播渠道", 1),  # 多行文本
                    ("发表日期", 1),  # 多行文本
                    ("内容标题", 1),  # 多行文本
                    ("阅读次数", 2),  # 数字
                    ("阅读次数占比", 1),  # 多行文本（百分比格式）
                ]
            elif table_type == "content_detail":
                fields_config = [
                    ("内容标题", 1),  # 多行文本
                    ("发表时间", 1),  # 多行文本
                    ("总阅读人数", 2),  # 数字
                    ("总阅读次数", 2),  # 数字
                    ("总分享人数", 2),  # 数字
                    ("总分享次数", 2),  # 数字
                    ("阅读后关注人数", 2),  # 数字
                    ("送达人数", 2),  # 数字
                    ("公众号消息阅读次数", 2),  # 数字
                    ("送达阅读率", 1),  # 多行文本（百分比格式）
                    ("首次分享次数", 2),  # 数字
                    ("分享产生阅读次数", 2),  # 数字
                    ("首次分享率", 1),  # 多行文本（百分比格式）
                    ("每次分享带来阅读次数", 2),  # 数字
                    ("阅读完成率", 1),  # 多行文本（百分比格式）
                    ("内容url", 15),  # 超链接
                ]
            elif table_type == "user_channel":
                fields_config = [
                    ("时间", 1),  # 多行文本
                    ("新关注人数", 2),  # 数字
                    ("取消关注人数", 2),  # 数字
                    ("净增关注人数", 2),  # 数字
                    ("累积关注人数", 2),  # 数字
                ]
            else:
                logger.warning(f"未知的表格类型: {table_type}")
                return table_id

            # 创建字段
            created_fields = []
            for field_name, field_type in fields_config:
                field_id = self.create_table_field(app_token, table_id, field_name, field_type)
                if field_id:
                    created_fields.append(field_name)
                    logger.info(f"成功创建字段: {field_name}")
                else:
                    logger.warning(f"创建字段失败: {field_name}")

            logger.info(f"表格 {table_name} 创建完成，共创建 {len(created_fields)} 个字段")
            return table_id

        except Exception as e:
            logger.error(f"创建表格和字段异常: {e}")
            return None

    def create_table(self, app_token: str, table_name: str, fields: Optional[List[Dict]] = None) -> Optional[str]:
        """在多维表格中创建数据表
        
        Args:
            app_token: 多维表格的app_token
            table_name: 数据表名称
            fields: 字段定义列表，可选
            
        Returns:
            创建成功返回table_id，失败返回None
        """
        try:
            # 构建请求体
            request_body = CreateAppTableRequest.builder() \
                .request_body(CreateAppTableRequestBody.builder()
                    .table(ReqTable.builder()
                        .name(table_name)
                        .build())
                    .build()) \
                .app_token(app_token) \
                .build()
            
            # 发起请求
            response = self.client.bitable.v1.app_table.create(request_body)
            
            if not response.success():
                logger.error(f"创建数据表失败: {response.code}, {response.msg}")
                return None
            
            table_id = response.data.table_id
            logger.info(f"成功创建数据表: {table_name}, table_id: {table_id}")
            return table_id
            
        except Exception as e:
            logger.error(f"创建数据表异常: {e}")
            return None
    
    def batch_create_records(self, app_token: str, table_id: str, records: List[Dict[str, Any]]) -> Optional[List[str]]:
        """批量创建记录
        
        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            records: 记录数据列表，每个记录是字段名到值的映射
            
        Returns:
            创建成功返回record_id列表，失败返回None
        """
        try:
            # 飞书API限制单次最多500条记录
            if len(records) > 500:
                logger.warning(f"记录数量({len(records)})超过500条限制，将只处理前500条")
                records = records[:500]
            
            # 构建记录数据
            app_table_records = []
            for record_data in records:
                # 将记录数据转换为飞书格式
                fields = {}
                for field_name, field_value in record_data.items():
                    fields[field_name] = field_value
                
                app_table_records.append(
                    AppTableRecord.builder()
                    .fields(fields)
                    .build()
                )
            
            # 构建请求
            request = BatchCreateAppTableRecordRequest.builder() \
                .request_body(BatchCreateAppTableRecordRequestBody.builder()
                    .records(app_table_records)
                    .build()) \
                .app_token(app_token) \
                .table_id(table_id) \
                .build()
            
            # 发起请求
            response = self.client.bitable.v1.app_table_record.batch_create(request)
            
            if not response.success():
                logger.error(f"批量创建记录失败: {response.code}, {response.msg}")
                return None
            
            record_ids = [record.record_id for record in response.data.records]
            logger.info(f"成功创建{len(record_ids)}条记录")
            return record_ids
            
        except Exception as e:
            logger.error(f"批量创建记录异常: {e}")
            return None
    
    def update_record(self, app_token: str, table_id: str, record_id: str, fields: Dict[str, Any]) -> bool:
        """更新单条记录

        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            record_id: 记录的record_id
            fields: 要更新的字段数据

        Returns:
            更新成功返回True，失败返回False
        """
        try:
            # 构建请求
            request = UpdateAppTableRecordRequest.builder() \
                .request_body(AppTableRecord.builder()
                    .fields(fields)
                    .build()) \
                .app_token(app_token) \
                .table_id(table_id) \
                .record_id(record_id) \
                .build()

            # 发起请求
            response = self.client.bitable.v1.app_table_record.update(request)
            
            if not response.success():
                logger.error(f"更新记录失败: {response.code}, {response.msg}")
                return False
            
            logger.info(f"成功更新记录: {record_id}")
            return True
            
        except Exception as e:
            logger.error(f"更新记录异常: {e}")
            return False
    
    def get_table_fields(self, app_token: str, table_id: str) -> Optional[List[Dict]]:
        """获取数据表的字段信息

        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id

        Returns:
            字段信息列表，失败返回None
        """
        try:
            # 构建请求
            request = ListAppTableFieldRequest.builder() \
                .app_token(app_token) \
                .table_id(table_id) \
                .build()

            # 发起请求
            response = self.client.bitable.v1.app_table_field.list(request)
            
            if not response.success():
                logger.error(f"获取表格字段失败: {response.code}, {response.msg}")
                return None
            
            fields = []
            for field in response.data.items:
                fields.append({
                    "field_id": field.field_id,
                    "field_name": field.field_name,
                    "type": field.type,
                    "property": field.property
                })
            
            logger.info(f"成功获取{len(fields)}个字段信息")
            return fields
            
        except Exception as e:
            logger.error(f"获取表格字段异常: {e}")
            return None
    
    def list_records(self, app_token: str, table_id: str, page_size: int = 100) -> Optional[List[Dict]]:
        """获取数据表中的记录

        Args:
            app_token: 多维表格的app_token
            table_id: 数据表的table_id
            page_size: 每页记录数，默认100

        Returns:
            记录列表，失败返回None
        """
        try:
            # 构建请求
            request = ListAppTableRecordRequest.builder() \
                .app_token(app_token) \
                .table_id(table_id) \
                .page_size(page_size) \
                .build()

            # 发起请求
            response = self.client.bitable.v1.app_table_record.list(request)
            
            if not response.success():
                logger.error(f"获取记录列表失败: {response.code}, {response.msg}")
                return None
            
            records = []
            for record in response.data.items:
                records.append({
                    "record_id": record.record_id,
                    "fields": record.fields,
                    "created_time": record.created_time,
                    "last_modified_time": record.last_modified_time
                })
            
            logger.info(f"成功获取{len(records)}条记录")
            return records
            
        except Exception as e:
            logger.error(f"获取记录列表异常: {e}")
            return None
