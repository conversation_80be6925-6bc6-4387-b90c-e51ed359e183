#!/usr/bin/env python3
"""
测试飞书字段创建功能
"""
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

def test_field_creation():
    """测试字段创建功能"""
    print("=" * 60)
    print("飞书字段创建测试")
    print("=" * 60)
    
    # 检查环境变量
    app_id = os.getenv("FEISHU_APP_ID")
    app_secret = os.getenv("FEISHU_APP_SECRET")
    
    if not app_id or not app_secret:
        print("❌ 请先设置环境变量 FEISHU_APP_ID 和 FEISHU_APP_SECRET")
        return False
    
    try:
        from app.services.feishu_service import FeishuService
        
        # 初始化飞书服务
        print("🔄 初始化飞书服务...")
        feishu_service = FeishuService()
        print("✅ 飞书服务初始化成功")
        
        # 创建测试多维表格
        print("\n🔄 创建测试多维表格...")
        import time
        bitable_name = f"字段测试表格_{int(time.time())}"
        app_token = feishu_service.create_bitable(bitable_name)
        
        if not app_token:
            print("❌ 创建多维表格失败")
            return False
        
        print(f"✅ 多维表格创建成功: {app_token}")
        
        # 测试创建用户概况数据表（包含字段）
        print("\n🔄 创建用户概况数据表...")
        user_table_id = feishu_service.create_table_with_fields(app_token, "用户概况数据", "user_summary")
        
        if user_table_id:
            print(f"✅ 用户概况数据表创建成功: {user_table_id}")
            
            # 获取字段信息验证
            print("🔄 验证字段创建...")
            fields = feishu_service.get_table_fields(app_token, user_table_id)
            if fields:
                print(f"✅ 字段验证成功，共 {len(fields)} 个字段:")
                for field in fields:
                    print(f"   - {field.get('field_name', 'Unknown')}: {field.get('type', 'Unknown')}")
            else:
                print("❌ 字段验证失败")
        else:
            print("❌ 用户概况数据表创建失败")
            return False
        
        # 测试创建图文分析数据表（包含字段）
        print("\n🔄 创建图文分析数据表...")
        article_table_id = feishu_service.create_table_with_fields(app_token, "图文分析数据", "article_summary")
        
        if article_table_id:
            print(f"✅ 图文分析数据表创建成功: {article_table_id}")
            
            # 获取字段信息验证
            print("🔄 验证字段创建...")
            fields = feishu_service.get_table_fields(app_token, article_table_id)
            if fields:
                print(f"✅ 字段验证成功，共 {len(fields)} 个字段:")
                for field in fields:
                    print(f"   - {field.get('field_name', 'Unknown')}: {field.get('type', 'Unknown')}")
            else:
                print("❌ 字段验证失败")
        else:
            print("❌ 图文分析数据表创建失败")
            return False
        
        # 测试创建记录
        print("\n🔄 测试创建记录...")
        test_user_records = [
            {
                "日期": "2024-01-01",
                "新增用户数": 100,
                "取消关注用户数": 10,
                "净增长": 90,
                "累计用户数": 1000
            }
        ]
        
        user_record_ids = feishu_service.batch_create_records(app_token, user_table_id, test_user_records)
        if user_record_ids:
            print(f"✅ 用户记录创建成功: {len(user_record_ids)} 条")
        else:
            print("❌ 用户记录创建失败")
        
        test_article_records = [
            {
                "文章标题": "测试文章",
                "阅读数": 1000,
                "点赞数": 50,
                "分享数": 20,
                "发布时间": "2024-01-01 10:00:00",
                "送达人数": 800
            }
        ]
        
        article_record_ids = feishu_service.batch_create_records(app_token, article_table_id, test_article_records)
        if article_record_ids:
            print(f"✅ 图文记录创建成功: {len(article_record_ids)} 条")
        else:
            print("❌ 图文记录创建失败")
        
        print(f"\n🎉 测试完成！")
        print(f"   多维表格: {app_token}")
        print(f"   用户概况表: {user_table_id}")
        print(f"   图文分析表: {article_table_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("飞书字段创建功能测试")
    print("注意: 此测试需要有效的飞书应用凭证")
    
    success = test_field_creation()
    
    if success:
        print("\n🎉 所有测试通过！字段创建功能正常工作。")
        return 0
    else:
        print("\n❌ 测试失败！请检查配置和网络连接。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
